package com.sayweee.datasync.fetcher.jira.parser;

import com.atlassian.jira.rest.client.api.domain.*;
import com.sayweee.datasync.fetcher.jira.dto.JiraIngestionPayload;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Slf4j
@Component
@RequiredArgsConstructor
public class JiraPayloadParser {
    private final IssueParser issueParser;
    private final CommentParser commentParser;
    private final ChangelogParser changelogParser;

    public List<JiraIngestionPayload> parse(Iterable<Issue> issues) {
        log.info("Parsing Jira issues...");
        return StreamSupport.stream(issues.spliterator(), false)
                .map(this::parse)
                .collect(Collectors.toList());
    }

    private JiraIngestionPayload parse(Issue issue) {
        var issueEntity = issueParser.parse(issue);
        var comments = commentParser.parse(issue);
        var changelogs = changelogParser.parse(issue);
        return new JiraIngestionPayload(issueEntity, comments, changelogs);
    }
}